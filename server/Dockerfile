# Multi-stage build for production optimization
FROM node:20-alpine AS base

# Install system dependencies for Prisma and native modules
RUN apk add --no-cache \
    openssl \
    libc6-compat \
    dumb-init

# Create app directory and non-root user
WORKDIR /app
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

# Copy package files
COPY package.json yarn.lock ./
COPY prisma ./prisma/

# Install dependencies
FROM base AS deps
RUN yarn install --frozen-lockfile --production=false

# Build stage
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client and build application
RUN yarn prisma generate
RUN yarn build

# Production stage
FROM base AS runner

# Copy built application and dependencies
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma

# Create startup script for migrations and app launch
RUN cat > /app/start.sh << 'EOF'
#!/bin/sh
set -e

echo "🚀 Starting LocPay Backend..."
echo "📊 Environment: ${NODE_ENV:-production}"

# Run database migrations
echo "🔄 Running database migrations..."
yarn prisma migrate deploy

# Check if migrations were successful
if [ $? -eq 0 ]; then
    echo "✅ Database migrations completed successfully"
else
    echo "❌ Database migrations failed"
    exit 1
fi

# Generate Prisma client (in case of schema changes)
echo "🔧 Generating Prisma client..."
yarn prisma generate

# Start the application
echo "🎯 Starting NestJS application..."
exec dumb-init node dist/main
EOF

# Make startup script executable
RUN chmod +x /app/start.sh

# Change ownership to non-root user
RUN chown -R nestjs:nodejs /app
USER nestjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Expose port
EXPOSE 3000

# Use startup script as entrypoint
CMD ["/app/start.sh"]